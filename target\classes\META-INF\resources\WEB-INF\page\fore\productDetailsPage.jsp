<%@ page contentType="text/html;charset=UTF-8" %>
<%@ include file="include/header.jsp" %>
<head>
    <script src="${pageContext.request.contextPath}/res/js/fore/fore_productDetails.js"></script>
    <script src="${pageContext.request.contextPath}/res/js/fore/productDetails.js"></script>
    <script>
        $(function () {
            // 初始化商品详情渲染器
            var productId = ${requestScope.productId};
            var contextPath = '${pageContext.request.contextPath}';

            console.log('Product ID:', productId);
            console.log('Context Path:', contextPath);

            // 检查productId是否有效
            if (!productId || productId === 'null' || productId === '') {
                console.error('Product ID is invalid:', productId);
                alert('商品ID无效，请重新访问页面');
                return;
            }

            // 初始化商品详情数据渲染
            ProductDetailsRenderer.init(productId, contextPath);

            // 检查用户登录状态
            function checkUserLogin() {
                const token = localStorage.getItem('token') || sessionStorage.getItem('token');
                return token !== null && token !== '';
            }

            // 显示登录框
            function showLoginModal() {
                $(".loginModel").show();
                $(".loginDiv").show();
            }

            // 隐藏登录框
            function hideLoginModal() {
                $(".loginModel").hide();
                $(".loginDiv").hide();
            }

            // 绑定登录框关闭事件
            $(".closeLoginDiv, .loginModel").click(function() {
                hideLoginModal();
            });

            // 处理登录表单提交
            $(".loginForm").submit(function(e) {
                e.preventDefault();

                const username = $("#name").val().trim();
                const password = $("#password").val().trim();

                if (!username || !password) {
                    $("#error_message_p").text("请输入用户名和密码");
                    return;
                }

                $.ajax({
                    url: "/tmall/api/login",
                    type: "POST",
                    data: {
                        username: username,
                        password: password
                    },
                    dataType: "json",
                    success: function(response) {
                        if (response.code === 200) {
                            // 登录成功，保存token
                            localStorage.setItem('token', response.data.token);
                            hideLoginModal();
                            $("#error_message_p").text("");
                            // 刷新页面或重新执行之前的操作
                            location.reload();
                        } else {
                            $("#error_message_p").text(response.message || "登录失败");
                        }
                    },
                    error: function() {
                        $("#error_message_p").text("网络错误，请稍后重试");
                    }
                });
            });

            //点击购买按钮时
            $(".context_buy_form").submit(function () {
                if (!checkUserLogin()) {
                    showLoginModal();
                    return false;
                }
                var number = isNaN($.trim($(".context_buymember").val()));
                if (number) {
                    location.reload();
                } else {
                    // 先验证登录状态，再跳转
                    const token = localStorage.getItem('token') || sessionStorage.getItem('token');

                    $.ajax({
                        url: "/tmall/api/cart", // 使用购物车API来验证登录状态
                        type: "GET",
                        headers: {
                            'Authorization': 'Bearer ' + token
                        },
                        success: function(response) {
                            if (response.code === 200) {
                                // 登录状态有效，跳转到订单页面
                                location.href = "${pageContext.request.contextPath}/order/create/" + productId + "?product_number=" + $.trim($(".context_buymember").val());
                            } else {
                                // 登录状态无效
                                localStorage.removeItem('token');
                                sessionStorage.removeItem('token');
                                showLoginModal();
                            }
                        },
                        error: function(xhr) {
                            if (xhr.status === 401) {
                                // Token过期
                                localStorage.removeItem('token');
                                sessionStorage.removeItem('token');
                                showLoginModal();
                            } else {
                                // 其他错误，仍然尝试跳转
                                location.href = "${pageContext.request.contextPath}/order/create/" + productId + "?product_number=" + $.trim($(".context_buymember").val());
                            }
                        }
                    });
                }
                return false;
            });

            //点击加入购物车按钮时
            $(".context_buyCar_form").submit(function () {
                if (!checkUserLogin()) {
                    showLoginModal();
                    return false;
                }
                var number = isNaN($.trim($(".context_buymember").val()));
                if (number) {
                    location.reload();
                } else {
                    const token = localStorage.getItem('token') || sessionStorage.getItem('token');

                    $.ajax({
                        url: "${pageContext.request.contextPath}/orderItem/create/" + productId + "?product_number=" + $.trim($(".context_buymember").val()),
                        type: "POST",
                        headers: {
                            'Authorization': 'Bearer ' + token
                        },
                        data: {"product_number": number},
                        dataType: "json",
                        success: function (data) {
                            if (data.success) {
                                $(".msg").stop(true, true).animate({
                                    opacity: 1
                                }, 550, function () {
                                    $(".msg").animate({
                                        opacity: 0
                                    }, 1500);
                                });
                            } else {
                                if (data.url != null) {
                                    if (data.url.includes('login')) {
                                        // Token过期，清除并显示登录框
                                        localStorage.removeItem('token');
                                        sessionStorage.removeItem('token');
                                        showLoginModal();
                                    } else {
                                        location.href = "/tmall" + data.url;
                                    }
                                } else {
                                    alert("加入购物车失败，请稍后再试！");
                                }
                            }
                        },
                        error: function (xhr) {
                            if (xhr.status === 401) {
                                // Token过期，清除并显示登录框
                                localStorage.removeItem('token');
                                sessionStorage.removeItem('token');
                                showLoginModal();
                            } else {
                                alert("加入购物车失败，请稍后再试！");
                            }
                        }
                    });
                    return false;
                }
            });
        });

        // 重写getDetailsPage函数，添加评价组件初始化
        window.getDetailsPage = function(obj, className) {
            $(".J_TabBarBox").find("li").removeClass("tab-selected");
            $(obj).parent("li").addClass("tab-selected");
            $(".J_choose").children("div").hide();
            $("." + className).show();

            // 如果切换到评价标签，初始化评价组件
            if (className === 'J_reviews') {
                // 确保ReviewComponent已定义
                if (typeof ReviewComponent !== 'undefined') {
                    ReviewComponent.init(${requestScope.productId});
                }
            }
        };
    </script>
    <link href="${pageContext.request.contextPath}/res/css/fore/fore_productDetails.css" rel="stylesheet">
    <title>${requestScope.productName}-趣味商城</title>
</head>
<body>
<nav>
    <%@ include file="include/navigator.jsp" %>
    <div class="header">
<%--        <a href="${pageContext.request.contextPath}"><img--%>
<%--                src="${pageContext.request.contextPath}/res/images/fore/WebsiteImage/tmallLogoB.png"></a>--%>
        <span class="shopNameHeader">海涛官方旗舰店</span>
        <input id="tid" type="hidden" value=""/>
        <img src="${pageContext.request.contextPath}/res/images/fore/WebsiteImage/detailsHeaderA.png"
             class="shopAssessHeader">
        <div class="shopSearchHeader">
            <form action="${pageContext.request.contextPath}/product" method="get">
                <div class="shopSearchInput">
                    <input type="text" class="searchInput" name="product_name" placeholder="搜索  商品/品牌/店铺"
                           maxlength="50">
                    <input type="submit" value="搜趣味商城" class="searchTmall">
                </div>
                <input type="submit" value="搜本店" class="searchShop">
            </form>
            <ul>
                <!-- 分类导航将通过JavaScript动态加载 -->
            </ul>
        </div>
    </div>
</nav>
<div class="loginModel"></div>
<div class="loginDiv">
    <div class="loginDivHeader">
        <a href="javascript:void(0)" class="closeLoginDiv"></a>
    </div>

    <div class="pwdLogin">
        <span class="loginTitle">密码登录</span>
        <form method="post" class="loginForm">
            <div class="loginInputDiv">
                <label for="name" class="loginLabel"><img
                        src="${pageContext.request.contextPath}/res/images/fore/WebsiteImage/2018-04-27_235518.png"
                        width="38px" height="39px" title="会员名"/></label>
                <input type="text" name="name" id="name" class="loginInput" placeholder="会员名/邮箱/手机号">
            </div>
            <div class="loginInputDiv">
                <label for="password" class="loginLabel"><img
                        src="${pageContext.request.contextPath}/res/images/fore/WebsiteImage/2018-04-27_235533.png"
                        width="38px" height="39px" title="登录密码"/></label>
                <input type="password" name="password" id="password" class="loginInput">
            </div>
            <input type="submit" class="loginButton" value="登 录">
        </form>
        <div class="loginLinks">
            <a href="#">忘记密码</a>
            <a href="#">忘记会员名</a>
            <a href="${pageContext.request.contextPath}/register" target="_blank">免费注册</a>
        </div>
        <div class="error_message">
            <p id="error_message_p"></p>
        </div>
    </div>

</div>
<div class="shopImg">
    <img src="">
</div>
<div class="context">
    <div class="context_left">
        <div class="context_img_ks">
            <img src="" width="800px" height="800px">
        </div>
        <div class="context_img">
            <img src="" class="context_img_main" width="400px" height="400px"/>
            <div class="context_img_winSelector"></div>
        </div>
        <ul class="context_img_ul">
            <!-- 商品图片列表将通过JavaScript动态加载 -->
        </ul>
    </div>
    <div class="context_info">
        <div class="context_info_name_div">
            <p class="context_info_name">商品名称</p>
            <span class="context_info_title">商品标题</span>
        </div>
        <div class="context_info_main">
            <div class="context_info_main_ad">
                <img src="${pageContext.request.contextPath}/res/images/fore/WebsiteImage/context_ad.png">
                <span>全天猫实物商品通用</span>
                <a href="#">去刮券<img
                        src="${pageContext.request.contextPath}/res/images/fore/WebsiteImage/tmallItemContentB.png"></a>
            </div>
            <dl class="context_price_panel">
                <dt>价格</dt>
                <dd><em>¥</em><span>0</span></dd>
            </dl>
            <dl class="context_promotePrice_panel">
                <dt>促销价</dt>
                <dd><em>¥</em><span>0</span></dd>
            </dl>
        </div>
        <ul class="context_other_panel">
            <li>总销量<span>0</span></li>
            <li>累计评价<span>0</span></li>
            <li class="tmall_points">送天猫积分<span>0</span></li>
        </ul>
        <dl class="context_info_member">
            <dt>数量</dt>
            <dd>
                <input type="text" value="1" maxlength="8" title="请输入购买量" class="context_buymember">
                <input type="hidden" id="stock" value="1000">
                <span class="amount-btn">
                    <img src="${pageContext.request.contextPath}/res/images/fore/WebsiteImage/up.png"
                         class="amount_value_up">
                    <img src="${pageContext.request.contextPath}/res/images/fore/WebsiteImage/down.png"
                         class="amount_value-down">
                </span>
                <span class="amount_unit">件</span>
                <em>库存1000件</em>
            </dd>
        </dl>

        <div class="context_buy">
            <form method="get" class="context_buy_form">
                <input class="context_buyNow" type="submit" value="立即购买"/>
            </form>
            <form method="get" class="context_buyCar_form">
                <input class="context_addBuyCar" type="submit" value="加入购物车"/>
            </form>
        </div>
        <div class="context_clear">
            <span>服务承诺</span>
            <a href="#">正品保证</a>
            <a href="#">极速退款</a>
            <a href="#">七天无理由退换</a>
        </div>
    </div>
    <div class="context_ul">
        <div class="context_ul_head">
            <s></s>
            <span>看了又看</span>
        </div>
        <div class="context_ul_goodsList">
            <ul>
                <!-- 推荐商品列表将通过JavaScript动态加载 -->
            </ul>
            <input type="hidden" id="guessNumber" value="">
        </div>
        <ul class="context_ul_trigger">
            <li class="ul_trigger_up"><a href="#"></a></li>
            <li class="ul_trigger_down"><a href="#"></a></li>
        </ul>
    </div>
</div>
<div class="mainwrap">
    <div class="J_TabBarBox">
        <ul>
            <li class="J_GoodsDetails">
                <a href="javascript:void(0)" class="detailsClick" onclick="getDetailsPage(this,'J_details')">商品详情</a>
            </li>
            <li class="J_GoodsReviews">
                <a href="javascript:void(0)"
                   onclick="getDetailsPage(this,'J_reviews')">累计评价<span>0</span></a>
            </li>
        </ul>
    </div>
    <div class="J_choose">
        <%@include file="include/J_details.jsp" %>
        <%@include file="include/J_review.jsp" %>
    </div>
    <div class="J_img">
        <!-- 商品详情图片将通过JavaScript动态加载 -->
    </div>
</div>
<div class="msg">
    <span>商品已添加</span>
</div>
<%@ include file="include/footer_two.jsp" %>
<%@ include file="include/footer.jsp" %>
</body>