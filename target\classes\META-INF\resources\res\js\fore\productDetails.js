/**
 * 商品详情页面数据渲染JavaScript
 */
class ProductDetailsRenderer {
    constructor() {
        this.productId = null;
        this.productData = null;
        this.contextPath = '';
    }

    /**
     * 初始化
     */
    init(productId, contextPath) {
        this.productId = productId;
        this.contextPath = contextPath;
        this.loadProductDetails();
    }

    /**
     * 加载商品详情数据
     */
    loadProductDetails() {
        const self = this;
        
        $.ajax({
            url: this.contextPath + "/product/" + this.productId,
            type: "GET",
            dataType: "json",
            success: function(response) {
                if (response.success) {
                    self.productData = response;
                    self.renderProductDetails();
                } else {
                    console.error("获取商品详情失败:", response.message);
                    self.showError("商品信息加载失败");
                }
            },
            error: function(xhr, status, error) {
                console.error("请求失败:", error);
                self.showError("网络错误，请稍后重试");
            }
        });
    }

    /**
     * 渲染商品详情
     */
    renderProductDetails() {
        this.renderBasicInfo();
        this.renderImages();
        this.renderPriceInfo();
        this.renderSalesInfo();
        this.renderProperties();
        this.renderRecommendedProducts();
        this.renderDetailImages();
        this.renderCategoryNav();
    }

    /**
     * 渲染基本信息
     */
    renderBasicInfo() {
        const product = this.productData.product;
        
        // 设置商品名称和标题
        $('.context_info_name').text(product.product_name);
        $('.context_info_title').text(product.product_title);
        
        // 设置页面标题
        document.title = product.product_name + '-趣味商城';
        
        // 设置店铺名称
        $('.shopNameHeader').text('海涛' + product.product_category.category_name + '官方旗舰店');
        
        // 设置分类ID
        $('#tid').val(product.product_category.category_id);
        
        // 设置店铺背景图
        $('.shopImg img').attr('src', this.contextPath + '/res/images/item/categoryPicture/' + product.product_category.category_image_src);
    }

    /**
     * 渲染商品图片
     */
    renderImages() {
        const product = this.productData.product;
        
        if (product.singleProductImageList && product.singleProductImageList.length > 0) {
            const firstImage = product.singleProductImageList[0];
            const imagePath = this.contextPath + '/res/images/item/productSinglePicture/' + firstImage.productImage_src;
            
            // 设置主图
            $('.context_img_main').attr('src', imagePath);
            $('.context_img_ks img').attr('src', imagePath);
            
            // 渲染图片列表
            const imageListHtml = product.singleProductImageList.map(img => {
                return `<li class="context_img_li">
                    <img src="${this.contextPath}/res/images/item/productSinglePicture/${img.productImage_src}"/>
                </li>`;
            }).join('');
            
            $('.context_img_ul').html(imageListHtml);
            
            // 设置第一张图片为选中状态
            $('.context_img_li').eq(0).addClass('context_img_li_hover');
        }
    }

    /**
     * 渲染价格信息
     */
    renderPriceInfo() {
        const product = this.productData.product;
        
        // 设置原价
        $('.context_price_panel dd span').text(product.product_price + '0');
        
        // 设置促销价
        $('.context_promotePrice_panel dd span').text(product.product_sale_price + '0');
        
        // 设置天猫积分
        const points = Math.floor(product.product_sale_price / 10);
        $('.tmall_points span').text(points);
    }

    /**
     * 渲染销售信息
     */
    renderSalesInfo() {
        const product = this.productData.product;
        
        // 设置销量
        const saleCount = product.product_sale_count || 0;
        $('.context_other_panel li:first span').text(saleCount);
        
        // 设置评价数
        $('.context_other_panel li:nth-child(2) span').text(product.product_review_count);
        
        // 更新评价标签页的数量
        $('.J_GoodsReviews a span').text(product.product_review_count);
    }

    /**
     * 渲染商品属性
     */
    renderProperties() {
        const propertyList = this.productData.propertyList;

        if (propertyList && propertyList.length > 0) {
            const propertiesHtml = propertyList.map(property => {
                if (property.propertyValueList && property.propertyValueList.length > 0 &&
                    property.propertyValueList[0].propertyValue_value) {
                    const value = property.propertyValueList[0].propertyValue_value;
                    const name = property.property_name;
                    return `<li title="${this.escapeHtml(value)}">
                        ${this.escapeHtml(name)}：${this.escapeHtml(value)}
                    </li>`;
                }
                return '';
            }).filter(html => html !== '').join('');

            $('.J_details_list_body').html(propertiesHtml);
            $('.J_details_list_header span').text(this.productData.product.product_name);
        }
    }

    /**
     * HTML转义函数
     */
    escapeHtml(text) {
        if (!text) return '';
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    /**
     * 渲染推荐商品
     */
    renderRecommendedProducts() {
        const loveProductList = this.productData.loveProductList;
        
        if (loveProductList && loveProductList.length > 0) {
            const recommendHtml = loveProductList.map(product => {
                const imageUrl = product.singleProductImageList && product.singleProductImageList.length > 0 
                    ? this.contextPath + '/res/images/item/productSinglePicture/' + product.singleProductImageList[0].productImage_src
                    : '';
                
                return `<li class="context_ul_main">
                    <div class="context_ul_img">
                        <a href="/tmall/product/${product.product_id}">
                            <img src="${imageUrl}">
                        </a>
                        <p>¥${product.product_sale_price}0</p>
                    </div>
                </li>`;
            }).join('');
            
            $('.context_ul_goodsList ul').html(recommendHtml);
            $('#guessNumber').val(this.productData.guessNumber);
        }
    }

    /**
     * 渲染详情图片
     */
    renderDetailImages() {
        const product = this.productData.product;
        
        if (product.detailProductImageList && product.detailProductImageList.length > 0) {
            const detailImagesHtml = product.detailProductImageList.map(image => {
                return `<img src="${this.contextPath}/res/images/item/productDetailsPicture/${image.productImage_src}"/>`;
            }).join('');
            
            $('.J_img').html(detailImagesHtml);
        }
    }

    /**
     * 渲染分类导航
     */
    renderCategoryNav() {
        const categoryList = this.productData.categoryList;
        
        if (categoryList && categoryList.length > 0) {
            const categoryHtml = categoryList.map(category => {
                return `<li>
                    <a href="${this.contextPath}/product?category_id=${category.category_id}">${category.category_name}</a>
                </li>`;
            }).join('');
            
            $('.shopSearchHeader ul').html(categoryHtml);
        }
    }

    /**
     * 显示错误信息
     */
    showError(message) {
        alert(message);
        // 可以考虑跳转到404页面或首页
        // window.location.href = '/tmall/404';
    }
}

// 全局实例
window.ProductDetailsRenderer = new ProductDetailsRenderer();
